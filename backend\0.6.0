Looking in links: https://pypi.org/simple/
Requirement already satisfied: crawl4ai in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (0.6.3)
Requirement already satisfied: aiosqlite~=0.20 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (0.21.0)
Requirement already satisfied: lxml~=5.3 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (5.4.0)
Requirement already satisfied: litellm>=1.53.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (1.72.2)
Requirement already satisfied: numpy<3,>=1.26.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (2.3.0)
Requirement already satisfied: pillow~=10.4 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (10.4.0)
Requirement already satisfied: playwright>=1.49.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (1.52.0)
Requirement already satisfied: python-dotenv~=1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (1.0.0)
Requirement already satisfied: requests~=2.26 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (2.32.3)
Requirement already satisfied: beautifulsoup4~=4.12 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (4.13.4)
Requirement already satisfied: tf-playwright-stealth>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (1.1.2)
Requirement already satisfied: xxhash~=3.4 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (3.5.0)
Requirement already satisfied: rank-bm25~=0.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (0.2.2)
Requirement already satisfied: aiofiles>=24.1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (24.1.0)
Requirement already satisfied: colorama~=0.4 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (0.4.6)
Requirement already satisfied: snowballstemmer~=2.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (2.2.0)
Collecting pydantic>=2.10 (from crawl4ai)
  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Requirement already satisfied: pyOpenSSL>=24.3.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (25.1.0)
Requirement already satisfied: psutil>=6.1.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (7.0.0)
Requirement already satisfied: nltk>=3.9.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (3.9.1)
Requirement already satisfied: rich>=13.9.4 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (14.0.0)
Requirement already satisfied: cssselect>=1.2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (1.3.0)
Requirement already satisfied: httpx>=0.27.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (0.28.1)
Requirement already satisfied: fake-useragent>=2.0.3 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (2.2.0)
Requirement already satisfied: click>=8.1.7 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (8.2.1)
Requirement already satisfied: pyperclip>=1.8.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (1.9.0)
Requirement already satisfied: chardet>=5.2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (5.2.0)
Requirement already satisfied: aiohttp>=3.11.11 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (3.12.11)
Requirement already satisfied: brotli>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (1.1.0)
Requirement already satisfied: humanize>=4.10.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from crawl4ai) (4.12.3)
Requirement already satisfied: typing_extensions>=4.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiosqlite~=0.20->crawl4ai) (4.13.2)
Requirement already satisfied: soupsieve>1.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from beautifulsoup4~=4.12->crawl4ai) (2.7)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests~=2.26->crawl4ai) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests~=2.26->crawl4ai) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests~=2.26->crawl4ai) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from requests~=2.26->crawl4ai) (2025.4.26)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp>=3.11.11->crawl4ai) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp>=3.11.11->crawl4ai) (1.3.2)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp>=3.11.11->crawl4ai) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp>=3.11.11->crawl4ai) (1.6.2)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp>=3.11.11->crawl4ai) (6.4.4)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp>=3.11.11->crawl4ai) (0.3.1)
Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from aiohttp>=3.11.11->crawl4ai) (1.20.0)
Requirement already satisfied: anyio in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from httpx>=0.27.2->crawl4ai) (3.7.1)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from httpx>=0.27.2->crawl4ai) (1.0.9)
Requirement already satisfied: h11>=0.16 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from httpcore==1.*->httpx>=0.27.2->crawl4ai) (0.16.0)
Requirement already satisfied: importlib-metadata>=6.8.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from litellm>=1.53.1->crawl4ai) (8.7.0)
Requirement already satisfied: jinja2<4.0.0,>=3.1.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from litellm>=1.53.1->crawl4ai) (3.1.6)
Requirement already satisfied: jsonschema<5.0.0,>=4.22.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from litellm>=1.53.1->crawl4ai) (4.24.0)
Requirement already satisfied: openai>=1.68.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from litellm>=1.53.1->crawl4ai) (1.84.0)
Requirement already satisfied: tiktoken>=0.7.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from litellm>=1.53.1->crawl4ai) (0.9.0)
Requirement already satisfied: tokenizers in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from litellm>=1.53.1->crawl4ai) (0.21.1)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from jinja2<4.0.0,>=3.1.2->litellm>=1.53.1->crawl4ai) (3.0.2)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from jsonschema<5.0.0,>=4.22.0->litellm>=1.53.1->crawl4ai) (2025.4.1)
Requirement already satisfied: referencing>=0.28.4 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from jsonschema<5.0.0,>=4.22.0->litellm>=1.53.1->crawl4ai) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from jsonschema<5.0.0,>=4.22.0->litellm>=1.53.1->crawl4ai) (0.25.1)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pydantic>=2.10->crawl4ai) (0.7.0)
Collecting pydantic-core==2.33.2 (from pydantic>=2.10->crawl4ai)
  Using cached pydantic_core-2.33.2-cp311-cp311-win_amd64.whl.metadata (6.9 kB)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pydantic>=2.10->crawl4ai) (0.4.1)
Requirement already satisfied: zipp>=3.20 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from importlib-metadata>=6.8.0->litellm>=1.53.1->crawl4ai) (3.23.0)
Requirement already satisfied: joblib in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from nltk>=3.9.1->crawl4ai) (1.5.1)
Requirement already satisfied: regex>=2021.8.3 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from nltk>=3.9.1->crawl4ai) (2024.11.6)
Requirement already satisfied: tqdm in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from nltk>=3.9.1->crawl4ai) (4.67.1)
Requirement already satisfied: distro<2,>=1.7.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from openai>=1.68.2->litellm>=1.53.1->crawl4ai) (1.9.0)
Requirement already satisfied: jiter<1,>=0.4.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from openai>=1.68.2->litellm>=1.53.1->crawl4ai) (0.10.0)
Requirement already satisfied: sniffio in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from openai>=1.68.2->litellm>=1.53.1->crawl4ai) (1.3.1)
Requirement already satisfied: pyee<14,>=13 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from playwright>=1.49.0->crawl4ai) (13.0.0)
Requirement already satisfied: greenlet<4.0.0,>=3.1.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from playwright>=1.49.0->crawl4ai) (3.2.3)
Requirement already satisfied: cryptography<46,>=41.0.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pyOpenSSL>=24.3.0->crawl4ai) (45.0.3)
Requirement already satisfied: cffi>=1.14 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from cryptography<46,>=41.0.5->pyOpenSSL>=24.3.0->crawl4ai) (1.17.1)
Requirement already satisfied: pycparser in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from cffi>=1.14->cryptography<46,>=41.0.5->pyOpenSSL>=24.3.0->crawl4ai) (2.22)
Requirement already satisfied: markdown-it-py>=2.2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from rich>=13.9.4->crawl4ai) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from rich>=13.9.4->crawl4ai) (2.19.1)
Requirement already satisfied: mdurl~=0.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from markdown-it-py>=2.2.0->rich>=13.9.4->crawl4ai) (0.1.2)
Requirement already satisfied: fake-http-header<0.4.0,>=0.3.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tf-playwright-stealth>=1.1.0->crawl4ai) (0.3.5)
