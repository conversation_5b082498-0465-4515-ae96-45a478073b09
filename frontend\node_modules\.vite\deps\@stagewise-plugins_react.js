"use client";
import {
  u
} from "./chunk-BCKZVBEU.js";
import "./chunk-TITDT5VP.js";

// node_modules/@stagewise-plugins/react/dist/index.es.js
function ReactLogo() {
  return u(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "-11.5 -10.23174 23 20.46348",
      children: [
        u("title", { children: "React Logo" }),
        u("circle", { cx: "0", cy: "0", r: "2.05", fill: "currentColor" }),
        u("g", { stroke: "currentColor", "stroke-width": "1", fill: "none", children: [
          u("ellipse", { rx: "11", ry: "4.2" }),
          u("ellipse", { rx: "11", ry: "4.2", transform: "rotate(60)" }),
          u("ellipse", { rx: "11", ry: "4.2", transform: "rotate(120)" })
        ] })
      ]
    }
  );
}
var FunctionComponent = 0;
var ClassComponent = 1;
var HostComponent = 5;
function getReactComponentHierarchy(element) {
  var _a, _b;
  if (!element) {
    return null;
  }
  const components = [];
  const maxComponents = 3;
  const fiberKey = Object.keys(element).find(
    (key) => key.startsWith("__reactFiber$") || key.startsWith("__reactInternalInstance$")
  );
  if (!fiberKey) {
    return null;
  }
  let currentFiber = element[fiberKey];
  if (!currentFiber) {
    return null;
  }
  while (currentFiber && components.length < maxComponents) {
    let componentData = null;
    if (currentFiber.tag === ClassComponent || currentFiber.tag === FunctionComponent) {
      const componentDefinition = currentFiber.type;
      if (componentDefinition) {
        const name = componentDefinition.displayName || componentDefinition.name || ((_a = currentFiber._debugOwner) == null ? void 0 : _a.name) || // Check direct name on fiber
        "AnonymousComponent";
        componentData = { name, type: "regular" };
      }
    } else if (currentFiber.tag === HostComponent && currentFiber._debugOwner && ((_b = currentFiber._debugOwner.env) == null ? void 0 : _b.toLowerCase().includes("server"))) {
      componentData = { name: currentFiber._debugOwner.name, type: "rsc" };
    }
    if (componentData) {
      const alreadyExists = components.some(
        (c) => c.name === componentData.name && c.type === componentData.type
      );
      if (!alreadyExists) {
        components.push(componentData);
      }
    }
    currentFiber = currentFiber.return;
  }
  return components.length > 0 ? components : null;
}
function getSelectedElementAnnotation(element) {
  const hierarchy = getReactComponentHierarchy(element);
  if (hierarchy == null ? void 0 : hierarchy[0]) {
    return {
      annotation: `${hierarchy[0].name}${hierarchy[0].type === "rsc" ? " (RSC)" : ""}`
    };
  }
  return { annotation: null };
}
function getSelectedElementsPrompt(elements) {
  const selectedComponentHierarchies = elements.map(
    (e) => getReactComponentHierarchy(e)
  );
  if (selectedComponentHierarchies.some((h) => h.length > 0)) {
    const content = `This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.

  ${selectedComponentHierarchies.map((h, index) => {
      return `
<element index="${index + 1}">
  ${h.length === 0 ? "No React component as parent detected" : `React component tree (from closest to farthest, 3 closest elements): ${h.map((c) => `{name: ${c.name}, type: ${c.type}}`).join(" child of ")}`}
</element>
    `;
    })}
  `;
    return content;
  }
  return null;
}
var ReactPlugin = {
  displayName: "React",
  description: "This toolbar adds additional information and metadata for apps using React as a UI framework",
  iconSvg: u(ReactLogo, {}),
  pluginName: "react",
  onContextElementHover: getSelectedElementAnnotation,
  onContextElementSelect: getSelectedElementAnnotation,
  onPromptSend: (prompt) => ({
    contextSnippets: [
      {
        promptContextName: "elements-react-component-info",
        content: getSelectedElementsPrompt(prompt.contextElements)
      }
    ]
  })
};
export {
  ReactPlugin
};
//# sourceMappingURL=@stagewise-plugins_react.js.map
