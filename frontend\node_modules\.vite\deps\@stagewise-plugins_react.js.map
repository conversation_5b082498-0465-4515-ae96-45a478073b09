{"version": 3, "sources": ["../../@stagewise-plugins/react/dist/index.es.js"], "sourcesContent": ["\"use client\";\nimport { jsxs, jsx } from \"@stagewise/toolbar/plugin-ui/jsx-runtime\";\nfunction ReactLogo() {\n  return /* @__PURE__ */ jsxs(\n    \"svg\",\n    {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      viewBox: \"-11.5 -10.23174 23 20.46348\",\n      children: [\n        /* @__PURE__ */ jsx(\"title\", { children: \"React Logo\" }),\n        /* @__PURE__ */ jsx(\"circle\", { cx: \"0\", cy: \"0\", r: \"2.05\", fill: \"currentColor\" }),\n        /* @__PURE__ */ jsxs(\"g\", { stroke: \"currentColor\", \"stroke-width\": \"1\", fill: \"none\", children: [\n          /* @__PURE__ */ jsx(\"ellipse\", { rx: \"11\", ry: \"4.2\" }),\n          /* @__PURE__ */ jsx(\"ellipse\", { rx: \"11\", ry: \"4.2\", transform: \"rotate(60)\" }),\n          /* @__PURE__ */ jsx(\"ellipse\", { rx: \"11\", ry: \"4.2\", transform: \"rotate(120)\" })\n        ] })\n      ]\n    }\n  );\n}\nconst FunctionComponent = 0;\nconst ClassComponent = 1;\nconst HostComponent = 5;\nfunction getReactComponentHierarchy(element) {\n  var _a, _b;\n  if (!element) {\n    return null;\n  }\n  const components = [];\n  const maxComponents = 3;\n  const fiberKey = Object.keys(element).find(\n    (key) => key.startsWith(\"__reactFiber$\") || key.startsWith(\"__reactInternalInstance$\")\n  );\n  if (!fiberKey) {\n    return null;\n  }\n  let currentFiber = element[fiberKey];\n  if (!currentFiber) {\n    return null;\n  }\n  while (currentFiber && components.length < maxComponents) {\n    let componentData = null;\n    if (currentFiber.tag === ClassComponent || currentFiber.tag === FunctionComponent) {\n      const componentDefinition = currentFiber.type;\n      if (componentDefinition) {\n        const name = componentDefinition.displayName || componentDefinition.name || ((_a = currentFiber._debugOwner) == null ? void 0 : _a.name) || // Check direct name on fiber\n        \"AnonymousComponent\";\n        componentData = { name, type: \"regular\" };\n      }\n    } else if (currentFiber.tag === HostComponent && currentFiber._debugOwner && ((_b = currentFiber._debugOwner.env) == null ? void 0 : _b.toLowerCase().includes(\"server\"))) {\n      componentData = { name: currentFiber._debugOwner.name, type: \"rsc\" };\n    }\n    if (componentData) {\n      const alreadyExists = components.some(\n        (c) => c.name === componentData.name && c.type === componentData.type\n      );\n      if (!alreadyExists) {\n        components.push(componentData);\n      }\n    }\n    currentFiber = currentFiber.return;\n  }\n  return components.length > 0 ? components : null;\n}\nfunction getSelectedElementAnnotation(element) {\n  const hierarchy = getReactComponentHierarchy(element);\n  if (hierarchy == null ? void 0 : hierarchy[0]) {\n    return {\n      annotation: `${hierarchy[0].name}${hierarchy[0].type === \"rsc\" ? \" (RSC)\" : \"\"}`\n    };\n  }\n  return { annotation: null };\n}\nfunction getSelectedElementsPrompt(elements) {\n  const selectedComponentHierarchies = elements.map(\n    (e) => getReactComponentHierarchy(e)\n  );\n  if (selectedComponentHierarchies.some((h) => h.length > 0)) {\n    const content = `This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.\n\n  ${selectedComponentHierarchies.map((h, index) => {\n      return `\n<element index=\"${index + 1}\">\n  ${h.length === 0 ? \"No React component as parent detected\" : `React component tree (from closest to farthest, 3 closest elements): ${h.map((c) => `{name: ${c.name}, type: ${c.type}}`).join(\" child of \")}`}\n</element>\n    `;\n    })}\n  `;\n    return content;\n  }\n  return null;\n}\nconst ReactPlugin = {\n  displayName: \"React\",\n  description: \"This toolbar adds additional information and metadata for apps using React as a UI framework\",\n  iconSvg: /* @__PURE__ */ jsx(ReactLogo, {}),\n  pluginName: \"react\",\n  onContextElementHover: getSelectedElementAnnotation,\n  onContextElementSelect: getSelectedElementAnnotation,\n  onPromptSend: (prompt) => ({\n    contextSnippets: [\n      {\n        promptContextName: \"elements-react-component-info\",\n        content: getSelectedElementsPrompt(prompt.contextElements)\n      }\n    ]\n  })\n};\nexport {\n  ReactPlugin\n};\n"], "mappings": ";;;;;;;AAEA,SAAS,YAAY;AACnB,SAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,QACQ,EAAI,SAAS,EAAE,UAAU,aAAa,CAAC;AAAA,QACvC,EAAI,UAAU,EAAE,IAAI,KAAK,IAAI,KAAK,GAAG,QAAQ,MAAM,eAAe,CAAC;AAAA,QACnE,EAAK,KAAK,EAAE,QAAQ,gBAAgB,gBAAgB,KAAK,MAAM,QAAQ,UAAU;AAAA,UAC/E,EAAI,WAAW,EAAE,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,UACtC,EAAI,WAAW,EAAE,IAAI,MAAM,IAAI,OAAO,WAAW,aAAa,CAAC;AAAA,UAC/D,EAAI,WAAW,EAAE,IAAI,MAAM,IAAI,OAAO,WAAW,cAAc,CAAC;AAAA,QAClF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,SAAS,2BAA2B,SAAS;AAC3C,MAAI,IAAI;AACR,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,QAAM,aAAa,CAAC;AACpB,QAAM,gBAAgB;AACtB,QAAM,WAAW,OAAO,KAAK,OAAO,EAAE;AAAA,IACpC,CAAC,QAAQ,IAAI,WAAW,eAAe,KAAK,IAAI,WAAW,0BAA0B;AAAA,EACvF;AACA,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,MAAI,eAAe,QAAQ,QAAQ;AACnC,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,WAAW,SAAS,eAAe;AACxD,QAAI,gBAAgB;AACpB,QAAI,aAAa,QAAQ,kBAAkB,aAAa,QAAQ,mBAAmB;AACjF,YAAM,sBAAsB,aAAa;AACzC,UAAI,qBAAqB;AACvB,cAAM,OAAO,oBAAoB,eAAe,oBAAoB,UAAU,KAAK,aAAa,gBAAgB,OAAO,SAAS,GAAG;AAAA,QACnI;AACA,wBAAgB,EAAE,MAAM,MAAM,UAAU;AAAA,MAC1C;AAAA,IACF,WAAW,aAAa,QAAQ,iBAAiB,aAAa,iBAAiB,KAAK,aAAa,YAAY,QAAQ,OAAO,SAAS,GAAG,YAAY,EAAE,SAAS,QAAQ,IAAI;AACzK,sBAAgB,EAAE,MAAM,aAAa,YAAY,MAAM,MAAM,MAAM;AAAA,IACrE;AACA,QAAI,eAAe;AACjB,YAAM,gBAAgB,WAAW;AAAA,QAC/B,CAAC,MAAM,EAAE,SAAS,cAAc,QAAQ,EAAE,SAAS,cAAc;AAAA,MACnE;AACA,UAAI,CAAC,eAAe;AAClB,mBAAW,KAAK,aAAa;AAAA,MAC/B;AAAA,IACF;AACA,mBAAe,aAAa;AAAA,EAC9B;AACA,SAAO,WAAW,SAAS,IAAI,aAAa;AAC9C;AACA,SAAS,6BAA6B,SAAS;AAC7C,QAAM,YAAY,2BAA2B,OAAO;AACpD,MAAI,aAAa,OAAO,SAAS,UAAU,CAAC,GAAG;AAC7C,WAAO;AAAA,MACL,YAAY,GAAG,UAAU,CAAC,EAAE,IAAI,GAAG,UAAU,CAAC,EAAE,SAAS,QAAQ,WAAW,EAAE;AAAA,IAChF;AAAA,EACF;AACA,SAAO,EAAE,YAAY,KAAK;AAC5B;AACA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,+BAA+B,SAAS;AAAA,IAC5C,CAAC,MAAM,2BAA2B,CAAC;AAAA,EACrC;AACA,MAAI,6BAA6B,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG;AAC1D,UAAM,UAAU;AAAA;AAAA,IAEhB,6BAA6B,IAAI,CAAC,GAAG,UAAU;AAC7C,aAAO;AAAA,kBACK,QAAQ,CAAC;AAAA,IACvB,EAAE,WAAW,IAAI,0CAA0C,wEAAwE,EAAE,IAAI,CAAC,MAAM,UAAU,EAAE,IAAI,WAAW,EAAE,IAAI,GAAG,EAAE,KAAK,YAAY,CAAC,EAAE;AAAA;AAAA;AAAA,IAG1M,CAAC,CAAC;AAAA;AAEF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,cAAc;AAAA,EAClB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAyB,EAAI,WAAW,CAAC,CAAC;AAAA,EAC1C,YAAY;AAAA,EACZ,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc,CAAC,YAAY;AAAA,IACzB,iBAAiB;AAAA,MACf;AAAA,QACE,mBAAmB;AAAA,QACnB,SAAS,0BAA0B,OAAO,eAAe;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACF;", "names": []}